
> 近期笔记
```dataview
LIST
FROM "00周期笔记"
WHERE file.day <= date(today) AND file.day > date(today) - dur(5 days)
SORT file.day DESC
```

[[令牌.mdenc]]


##  [[探索.README]]

# 探索好用的 ai 编程方式
- **Cursor**：适合日常代码开发和提高编码效率。
- **Windsurf**：适合复杂的多步编程和企业级开发。
- **v0**：适合快速搭建前端界面原型。
- **Bolt.new**：适合快速验证想法和非开发人员创建应用。
- **Lovable**：适合快速打造 MVP 和将设计变为交互产品。
[提示词项目]([GitHub - x1xhlol/system-prompts-and-models-of-ai-tools: FULL v0, <PERSON>ursor, Manus, Same.dev, Lovable, Devin, Replit Agent, Windsurf Agent & VSCode Agent (And other Open Sourced) System Prompts, Tools & AI Models.](https://github.com/x1xhlol/system-prompts-and-models-of-ai-tools))：
[通过CE修改器来跑满百度网盘和迅雷的下载速度！ Cheat Engine 7.5](https://www.freedidi.com/8852.html)

# 进行中的项目：
```dataview
TABLE without ID 
	file.link AS "项目",
	dateformat(deadline, "yyyy-MM-dd") AS "截止日期",
	goal AS "目标",
	status AS "状态"
FROM #project AND !"_Templates"
WHERE status = "ongoing"
SORT deadline ASC
```

# 目标列表
```dataview
TABLE
	without ID 
	file.link AS "目标",
	area AS "领域",
	dateformat(deadline, "yyyy-MM-dd") AS "截止日期",
	priority AS "优先级",
	status AS "状态"
FROM #goal AND !"_Templates" 
WHERE status = "active" 
SORT priority ASC
SORT deadline ASC
```

# 项目待办
[[领域Canvas.canvas|主页]]

```dataview
task
FROM !"_Templates" AND !"目标看板" AND #project
WHERE !completed
GROUP BY file.link
```

# 目标里程碑

```dataview
task
FROM !"_Templates" AND !"目标看板" AND #goal 
WHERE !completed
SORT file.frontmatter.priority
GROUP BY file.link
```

